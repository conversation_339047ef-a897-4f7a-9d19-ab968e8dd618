{"name": "swoop", "version": "1.0.0", "scripts": {"dev": "vite", "dev:mobile": "vite --host", "build": "vite build", "preview": "vite preview", "server": "node server/index.js", "simulate": "node src/sim/simulate.js", "ui:play": "node scripts/ui_autoplay.js", "ui:play:watch": "HEADLESS=0 SLOW_MO=250 node scripts/ui_autoplay.js", "ui:play:slow": "HEADLESS=0 SLOW_MO=400 TURN_PAUSE_MS=1500 node scripts/ui_autoplay.js", "test": "echo 'No tests defined' && exit 0", "deploy": "npm run build && gh-pages -d dist"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "cors": "^2.8.5", "express": "^4.19.2", "serverless-http": "^3.2.0", "ws": "^8.18.0"}, "devDependencies": {"@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.0", "playwright": "^1.47.0", "postcss": "^8.4.0", "tailwindcss": "^3.3.0", "vite": "^4.4.0"}}