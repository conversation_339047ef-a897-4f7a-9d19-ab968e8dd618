<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="Swoop" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="theme-color" content="#2563eb" />
    <title>Swoop - Mobile Game</title>
    <style>
      /* Default: do not show the orientation hint */
      .orientation-hint { display: none; }

      /* Prevent iOS bounce scrolling and zoom — mobile only */
      @media (hover: none) and (pointer: coarse) {
        html, body {
          height: 100%;
          overflow: hidden;
          position: fixed;
          width: 100%;
          -webkit-overflow-scrolling: touch;
          -webkit-user-select: none;
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: transparent;
        }
      }

      /* Show orientation hint only on small portrait touch screens */
      @media screen and (orientation: portrait) and (max-width: 900px) and (hover: none) and (pointer: coarse) {
        .orientation-hint {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: #1f2937;
          color: white;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          z-index: 9999;
          font-family: system-ui, -apple-system, sans-serif;
          text-align: center;
          padding: 20px;
        }
        .orientation-hint .rotate-icon {
          font-size: 4rem;
          margin-bottom: 1rem;
          animation: rotate 2s infinite ease-in-out;
        }
        @keyframes rotate {
          0%, 100% { transform: rotate(0deg); }
          50% { transform: rotate(90deg); }
        }
      }
    </style>
  </head>
  <body>
    <!-- Orientation hint for portrait mode -->
    <div class="orientation-hint">
      <div class="rotate-icon">📱</div>
      <h2>Please rotate your device</h2>
      <p>Swoop is designed for landscape mode</p>
    </div>

    <div id="root"></div>
    <script>
      // Optional runtime backend URL configuration for static hosting (e.g., GitHub Pages)
      // Usage: add ?backend=https://your-backend.example.com to the URL once
      // It will persist in localStorage and be picked up by the app.
      (function(){
        try {
          var p = new URLSearchParams(location.search);
          var b = p.get('backend');
          if (b) {
            window.SW_BACKEND_URL = b;
            try { localStorage.setItem('SW_BACKEND_URL', b); } catch(_){}
          } else {
            var saved = null; try { saved = localStorage.getItem('SW_BACKEND_URL'); } catch(_){}
            if (saved) window.SW_BACKEND_URL = saved;
          }
        } catch(_){}
      })();
    </script>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
