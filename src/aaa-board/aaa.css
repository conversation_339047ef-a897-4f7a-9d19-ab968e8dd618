/* AAA Skin for Swoop — focused on visuals only */
/* Scope everything under body.skin-aaa to avoid impacting default skin */

body.skin-aaa {
  --aaa-water: #0f8aa7;
  --aaa-stone: #e0d1af;
  --aaa-gold: #f1c75c;
  --aaa-danger: #d06b4e;
  --aaa-ink: #1b1b1b;
  background: #0b3b49;
}

.aaa-intro-cover {
  position: fixed;
  inset: 0;
  background: #0b3b49;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: min(4vw, 4vh);
  z-index: 999;
  cursor: pointer;
}
.aaa-intro-cover img {
  max-width: min(96vw, 1400px);
  max-height: 96vh;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 12px;
  box-shadow: 0 24px 48px rgba(0,0,0,0.45);
}
body.skin-aaa .aaa-intro-cover {
  background: #0b3b49;
}

/* Full bleed board */
body.skin-aaa .mobile-main-layout {
  padding: 0;
  align-items: stretch;
}
body.skin-aaa .mobile-board-container {
  flex: 1;
  align-items: stretch;
  justify-content: center;
}
body.skin-aaa .mobile-board {
  position: relative;
  background: #0b3b49 url('/RnD/bg3.png') center/cover no-repeat;
  border: none;
  border-radius: 0;
  box-shadow: none;
  padding: 0 !important;
  height: 100%;
  max-height: none;
  display: flex;
  align-items: stretch;
  justify-content: center;
}

/* Beach/bottom flourish */
body.skin-aaa .mobile-board::after {
  content: '';
  position: absolute;
  left: 0; right: 0; bottom: 0;
  height: 10%;
  background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.15));
  pointer-events: none;
}

/* Grid spacing tweaks to suit connectors */
body.skin-aaa .mobile-grid {
  gap: 2px;
  align-self: stretch;
}

/* Remove step numbers in AAA mode */
body.skin-aaa .mobile-step-number { display: none !important; }

/* Tile styling — replace solid colors with textured look */
body.skin-aaa .swoop-tile {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(0.5px);
  box-shadow: 0 4px 10px rgba(0,0,0,0.25), inset 0 0 0 1px rgba(0,0,0,0.15);
  border-radius: 8px;
}
body.skin-aaa .swoop-tile.swoop-cp {
  background: linear-gradient(180deg, rgba(255,232,181,0.9), rgba(210,164,72,0.85)), url('/RnD/tile2.png') center/105% no-repeat !important;
  box-shadow: 0 4px 10px rgba(0,0,0,0.25), inset 0 0 0 2px rgba(112, 73, 16, 0.4);
}
body.skin-aaa .swoop-tile.swoop-det {
  background: linear-gradient(180deg, rgba(222,104,70,0.9), rgba(140,52,27,0.85)), url('/RnD/deterrent.png') center/110% no-repeat !important;
  box-shadow: 0 4px 10px rgba(0,0,0,0.25), inset 0 0 0 2px rgba(79, 24, 12, 0.45);
}

/* Center tiles look more polished */
body.skin-aaa .swoop-center {
  filter: saturate(1.05);
}

/* Pieces — replace emoji with coins; keep ring as glow */
body.skin-aaa .swoop-piece {
  position: absolute;
  inset: 2px;
  border-radius: 999px;
  background: center/contain no-repeat;
  z-index: 12;
}
body.skin-aaa .swoop-piece > span { opacity: 0 !important; }
body.skin-aaa .swoop-piece[data-player="0"] { background-image: url('/RnD/img21.png'); }
body.skin-aaa .swoop-piece[data-player="0"].active { background-image: url('/RnD/img22.png'); }
body.skin-aaa .swoop-piece[data-player="0"].carry { background-image: url('/RnD/img5.png'); }
body.skin-aaa .swoop-piece[data-player="1"] { background-image: url('/RnD/img24.png'); }
body.skin-aaa .swoop-piece[data-player="1"].active { background-image: url('/RnD/img23.png'); }
body.skin-aaa .swoop-piece[data-player="1"].carry { background-image: url('/RnD/img1.png'); }
body.skin-aaa .swoop-piece[data-player="2"] { background-image: url('/RnD/crab_inactive1.png'); }
body.skin-aaa .swoop-piece[data-player="2"].active { background-image: url('/RnD/crab_active1.png'); }
body.skin-aaa .swoop-piece[data-player="2"].carry { background-image: url('/RnD/crab_carrying_basket1.png'); }
body.skin-aaa .swoop-piece[data-player="3"] { background-image: url('/RnD/turtle_inactive1.png'); }
body.skin-aaa .swoop-piece[data-player="3"].active { background-image: url('/RnD/turtle_active1.png'); }
body.skin-aaa .swoop-piece[data-player="3"].carry { background-image: url('/RnD/turtle_with_basket1.png'); }

body.skin-aaa .swoop-piece.carry::after { display: none; }
body.skin-aaa .swoop-basket {
  position: absolute;
  right: 0; top: 0;
  width: 56%; height: 56%;
  background: url('/RnD/img13.png') center/contain no-repeat;
  filter: drop-shadow(0 1px 2px rgba(0,0,0,0.5));
  z-index: 4;
}
body.skin-aaa .swoop-basket::after { content: none; }

/* Active ring becomes a soft glow */
body.skin-aaa .swoop-ring {
  inset: -1px;
  border-radius: 999px;
  border: none;
  box-shadow: 0 0 0 3px rgba(37,99,235,0.7), 0 0 16px rgba(37,99,235,0.7);
  z-index: 11;
}

/* Highlights refined */
body.skin-aaa .swoop-highlight {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255,255,255,0.75), 0 0 18px rgba(255,255,255,0.6) !important;
  transform: scale(1.06);
}

/* Compact HUD overlay */
body.skin-aaa .mobile-controls-container {
  position: absolute;
  right: 12px;
  top: 12px;
  width: clamp(220px, 42vw, 300px);
  max-width: calc(100vw - 24px);
  background: rgba(20,24,28,0.72);
  color: #fff;
  border: 1px solid rgba(255,255,255,0.12);
  border-radius: 12px;
  padding: 8px;
  z-index: 20;
}
body.skin-aaa .mobile-controls-container .mobile-button {
  background: rgba(255,255,255,0.12);
  color: #fff;
}
body.skin-aaa .mobile-controls-container .mobile-button.primary {
  background: rgba(37, 99, 235, 0.85);
}

/* Dice & sums panel — give it a carved obsidian console look */
body.skin-aaa .mobile-dice-section {
  margin-top: 6px;
  padding: 8px 10px;
  border-radius: 14px;
  background: linear-gradient(180deg, rgba(13,27,36,0.92), rgba(7,16,22,0.94));
  border: 1px solid rgba(255,255,255,0.15);
  box-shadow: 0 12px 24px rgba(0,0,0,0.45);
  color: rgba(255, 250, 235, 0.92);
}
body.skin-aaa .mobile-dice-container {
  display: flex;
  justify-content: center;
  gap: 4px;
  flex-wrap: wrap;
  width: 100%;
  margin-bottom: 8px;
  padding: 4px 8px;
  border-radius: 999px;
  background: linear-gradient(180deg, rgba(27,52,70,0.9), rgba(12,26,36,0.9));
  border: 1px solid rgba(255,255,255,0.18);
  box-shadow: inset 0 0 12px rgba(0,0,0,0.35);
}
body.skin-aaa .mobile-die {
  width: 24px;
  height: 24px;
  flex: 0 1 24px;
  border-radius: 10px;
  background: radial-gradient(circle at 30% 30%, rgba(255,255,255,0.35), rgba(80,140,170,0.3) 45%, rgba(20,46,61,0.95));
  border: 1px solid rgba(255,255,255,0.28);
  box-shadow: 0 6px 12px rgba(0,0,0,0.45), inset 0 0 6px rgba(255,255,255,0.25);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 800;
  color: #fdf7e3;
}
body.skin-aaa .mobile-pairs-container {
  display: flex;
  flex-direction: column;
  gap: 7px;
}
body.skin-aaa .mobile-pair {
  background: linear-gradient(180deg, rgba(20,33,43,0.92), rgba(9,18,25,0.92));
  border: 1px solid rgba(255,255,255,0.12);
  border-radius: 12px;
  padding: 7px 9px;
  box-shadow: 0 8px 16px rgba(0,0,0,0.4);
  color: rgba(255, 250, 235, 0.92);
}
body.skin-aaa .mobile-pair.selected {
  border-color: rgba(241, 199, 92, 0.55);
  box-shadow: 0 0 0 1px rgba(241, 199, 92, 0.35), 0 12px 22px rgba(0,0,0,0.45);
  background: linear-gradient(180deg, rgba(37,60,78,0.96), rgba(18,32,44,0.94));
}
body.skin-aaa .pair-sum {
  font-size: 0.75rem;
  font-weight: 800;
  letter-spacing: 0.08em;
  color: #ffe8b0;
  text-shadow: 0 3px 8px rgba(0,0,0,0.45);
}
body.skin-aaa .pair-divider {
  font-size: 0.55rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.08em;
  background: rgba(241, 199, 92, 0.2);
  color: rgba(255, 232, 176, 0.9);
  padding: 2px 6px;
  border-radius: 999px;
  align-self: center;
  white-space: nowrap;
}
body.skin-aaa .pair-calc,
body.skin-aaa .pair-reason {
  color: rgba(255,255,255,0.6);
}
body.skin-aaa .pair-reason {
  margin-top: 4px;
  font-size: 0.58rem;
  letter-spacing: 0.04em;
}
body.skin-aaa .pair-actions .mobile-button {
  min-width: 22px;
  min-height: 22px;
  border-radius: 8px;
  padding: 2px 6px;
  font-size: 0.6rem;
  box-shadow: inset 0 0 0 1px rgba(255,255,255,0.12);
}
body.skin-aaa .pair-actions .mobile-button.primary {
  background: linear-gradient(180deg, rgba(37, 124, 235, 0.9), rgba(15, 70, 168, 0.9));
  box-shadow: 0 6px 10px rgba(6,44,94,0.45);
}
body.skin-aaa .pair-actions .mobile-button.ghost,
body.skin-aaa .pair-actions .mobile-button:not(.primary) {
  background: rgba(21,36,48,0.85);
  color: rgba(255,255,255,0.8);
}
body.skin-aaa .pair-actions .mobile-button:disabled {
  opacity: 0.35;
  box-shadow: none;
}

/* Connectors overlay */
body.skin-aaa .aaa-connectors {
  position: absolute;
  inset: 0;
  z-index: 5;
  pointer-events: none;
}
body.skin-aaa .aaa-connectors line {
  stroke: rgba(255,255,255,0.25);
  stroke-width: 2.6;
  stroke-linecap: round;
}
body.skin-aaa .aaa-connectors .aaa-connector-highlight {
  stroke: rgba(255,255,255,0.3);
  stroke-width: 3;
}

body.skin-aaa .aaa-final-lane-labels {
  position: absolute;
  inset: 0;
  pointer-events: none;
  z-index: 7;
}
body.skin-aaa .aaa-final-lane-label {
  position: absolute;
  transform: translateY(-50%);
  padding: 4px 10px;
  min-width: 42px;
  text-align: center;
  border-radius: 999px;
  background: linear-gradient(180deg, rgba(13,39,55,0.95), rgba(6,20,28,0.9));
  border: 1px solid rgba(255,255,255,0.16);
  box-shadow: 0 6px 14px rgba(0,0,0,0.45), inset 0 0 0 1px rgba(255,255,255,0.08);
  color: rgba(255,246,229,0.95);
  font-weight: 800;
  font-size: clamp(13px, 1.2vw, 18px);
  letter-spacing: 0.15em;
}


/* Route/lane labels — make them read like engraved pillars at the banks */
body.skin-aaa .mobile-label {
  position: relative;
  z-index: 1;
  font-size: clamp(14px, 1.8vw, 26px);
  font-weight: 800;
  letter-spacing: 0.18em;
  color: rgba(255, 247, 234, 0.95);
  text-shadow: 0 4px 10px rgba(0,0,0,0.7);
  overflow: visible;
}
body.skin-aaa .mobile-label::before {
  content: '';
  position: absolute;
  inset: 14% 26%;
  border-radius: 999px;
  background: linear-gradient(180deg, rgba(13, 39, 55, 0.92), rgba(6, 20, 28, 0.95));
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 10px 18px rgba(0, 0, 0, 0.55);
  transform: scaleY(1.6);
  z-index: -1;
}
body.skin-aaa .mobile-label::after {
  content: '';
  position: absolute;
  inset: 23% 36%;
  border-radius: 999px;
  background: linear-gradient(180deg, rgba(241, 199, 92, 0.6), rgba(241, 199, 92, 0));
  opacity: 0.6;
  transform: scaleY(1.4);
  z-index: -1;
}

/* Carry indicator: show a clear basket overlay on carried pieces */
/* Hide arrow indicator in AAA mode to reduce clutter */
body.skin-aaa .mobile-carry-indicator { display: none !important; }

/* Hide top header for true fullscreen board */
body.skin-aaa .mobile-header { display: none !important; }

/* Status card */
body.skin-aaa .aaa-status {
  background: rgba(255,255,255,0.06);
  border: 1px solid rgba(255,255,255,0.18);
  border-radius: 8px;
  padding: 6px 8px;
  color: #fff;
  font-size: 12px;
}
body.skin-aaa .aaa-status .aaa-status-row {
  display: flex; justify-content: space-between; align-items: center; gap: 8px; margin: 3px 0;
}
body.skin-aaa .aaa-status .aaa-status-row.small { opacity: 0.8; font-size: 11px; }

/* Room toggle shows the multiplayer bar */
body.skin-aaa .aaa-mp-bar { display: none !important; }
body.skin-aaa.aaa-room-visible .aaa-mp-bar {
  display: flex !important; position: absolute; top: 8px; left: 8px; right: auto; z-index: 40;
  background: rgba(17,17,17,0.92); border: 1px solid rgba(255,255,255,0.12); border-radius: 8px; padding: 6px;
}
